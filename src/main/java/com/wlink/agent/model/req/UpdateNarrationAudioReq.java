package com.wlink.agent.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 修改旁白音频请求
 */
@Data
@Schema(description = "修改旁白音频请求")
public class UpdateNarrationAudioReq {

    @NotBlank(message = "会话ID不能为空")
    @Schema(description = "会话ID", required = true)
    @JsonProperty("sessionId")
    private String sessionId;

    @Schema(description = "章节ID数组，如果为空则处理所有章节")
    @JsonProperty("segmentIds")
    private List<String> segmentIds;

    @Schema(description = "音色ID，如果不传则使用会话默认音色")
    @JsonProperty("soundId")
    @NotNull(message = "音色ID不能为空")
    private Long soundId;

    @Schema(description = "角色ID，如果不传则修改旁白音频，如果传了则修改该角色的音频")
    @JsonProperty("characterId")
    private String characterId;
}
